<?php

namespace App\Filament\Pos\Resources;

use App\Filament\Pos\Resources\PosTransactionResource\Pages;
use App\Filament\Pos\Resources\PosTransactionResource\RelationManagers;
use App\Models\PosTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PosTransactionResource extends Resource
{
    protected static ?string $model = PosTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    protected static ?string $navigationGroup = 'POS Management';

    protected static ?string $navigationLabel = 'Transactions';

    protected static ?string $modelLabel = 'Transaction';

    protected static ?string $pluralModelLabel = 'Transactions';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Transaction Information')
                    ->schema([
                        Forms\Components\TextInput::make('transaction_number')
                            ->label('Transaction Number')
                            ->required()
                            ->unique(PosTransaction::class, 'transaction_number', ignoreRecord: true)
                            ->maxLength(255),

                        Forms\Components\Select::make('customer_id')
                            ->label('Customer')
                            ->relationship('customer', 'nama')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('nama')
                                    ->label('Customer Name')
                                    ->required(),
                                Forms\Components\TextInput::make('email')
                                    ->label('Email')
                                    ->email(),
                                Forms\Components\TextInput::make('telepon')
                                    ->label('Phone')
                                    ->tel(),
                            ]),

                        Forms\Components\Select::make('user_id')
                            ->label('Cashier')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('outlet_id')
                            ->label('Outlet')
                            ->relationship('outlet', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('order_type')
                            ->label('Order Type')
                            ->options([
                                'dine_in' => 'Dine In',
                                'takeaway' => 'Takeaway',
                                'delivery' => 'Delivery',
                                'pickup' => 'Pickup',
                            ])
                            ->placeholder('Select order type'),

                        Forms\Components\DateTimePicker::make('transaction_date')
                            ->label('Transaction Date')
                            ->required()
                            ->default(now()),

                        Forms\Components\TextInput::make('table_number')
                            ->label('Table Number')
                            ->maxLength(50),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Payment Information')
                    ->schema([
                        Forms\Components\TextInput::make('total_amount')
                            ->label('Subtotal')
                            ->numeric()
                            ->prefix('Rp')
                            ->required()
                            ->minValue(0),

                        Forms\Components\TextInput::make('discount_amount')
                            ->label('Discount Amount')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0)
                            ->minValue(0),

                        Forms\Components\TextInput::make('tax_amount')
                            ->label('Tax Amount')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0)
                            ->minValue(0),

                        Forms\Components\TextInput::make('net_amount')
                            ->label('Total Amount')
                            ->numeric()
                            ->prefix('Rp')
                            ->required()
                            ->minValue(0),

                        Forms\Components\Select::make('payment_method')
                            ->label('Payment Method')
                            ->options([
                                'cash' => 'Cash',
                                'card' => 'Card',
                                'digital_wallet' => 'Digital Wallet',
                                'bank_transfer' => 'Bank Transfer',
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('amount_paid')
                            ->label('Amount Paid')
                            ->numeric()
                            ->prefix('Rp')
                            ->required()
                            ->minValue(0),

                        Forms\Components\TextInput::make('change_given')
                            ->label('Change Given')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0)
                            ->minValue(0),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Loyalty & Additional Info')
                    ->schema([
                        Forms\Components\TextInput::make('loyalty_points_used')
                            ->label('Loyalty Points Used')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),

                        Forms\Components\TextInput::make('loyalty_points_earned')
                            ->label('Loyalty Points Earned')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),

                        Forms\Components\Toggle::make('is_offline_transaction')
                            ->label('Offline Transaction')
                            ->default(false),

                        Forms\Components\DateTimePicker::make('synced_at')
                            ->label('Synced At')
                            ->visible(fn($get) => $get('is_offline_transaction')),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) =>
                $query->with(['customer:id,nama', 'outlet:id,name'])
                    ->select([
                        'id', 'transaction_number', 'outlet_id', 'customer_id',
                        'transaction_date', 'total_amount', 'net_amount', 'payment_method',
                        'created_at'
                    ])
            )
            ->columns([
                Tables\Columns\TextColumn::make('transaction_number')
                    ->label('Transaction #')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->copyable(),

                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Date & Time')
                    ->dateTime('M j, Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('customer.nama')
                    ->label('Customer')
                    ->searchable()
                    ->placeholder('Walk-in Customer')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Cashier')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('outlet.name')
                    ->label('Outlet')
                    ->searchable()
                    ->badge()
                    ->color('info')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('order_type')
                    ->label('Order Type')
                    ->badge()
                    ->color(fn(?string $state): string => match ($state) {
                        'dine_in' => 'success',
                        'takeaway' => 'warning',
                        'delivery' => 'info',
                        'pickup' => 'primary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(?string $state): string => match ($state) {
                        'dine_in' => 'Dine In',
                        'takeaway' => 'Takeaway',
                        'delivery' => 'Delivery',
                        'pickup' => 'Pickup',
                        default => 'N/A',
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Payment')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'cash' => 'success',
                        'card' => 'info',
                        'digital_wallet' => 'warning',
                        'bank_transfer' => 'primary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Subtotal')
                    ->money('IDR')
                    ->alignEnd()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('discount_amount')
                    ->label('Discount')
                    ->money('IDR')
                    ->alignEnd()
                    ->toggleable()
                    ->color('danger'),

                Tables\Columns\TextColumn::make('net_amount')
                    ->label('Total')
                    ->money('IDR')
                    ->alignEnd()
                    ->weight('medium')
                    ->sortable(),

                Tables\Columns\TextColumn::make('table_number')
                    ->label('Table')
                    ->badge()
                    ->placeholder('Takeaway')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('items_count')
                    ->label('Items')
                    ->counts('items')
                    ->alignEnd()
                    ->badge()
                    ->color('info'),

                Tables\Columns\IconColumn::make('is_offline_transaction')
                    ->label('Sync Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-wifi')
                    ->falseIcon('heroicon-o-cloud')
                    ->trueColor('warning')
                    ->falseColor('success')
                    ->tooltip(fn($record) => $record->is_offline_transaction ? 'Offline Transaction' : 'Online Transaction'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('payment_method')
                    ->options([
                        'cash' => 'Cash',
                        'card' => 'Card',
                        'digital_wallet' => 'Digital Wallet',
                        'bank_transfer' => 'Bank Transfer',
                    ]),

                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Cashier')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('outlet_id')
                    ->label('Outlet')
                    ->relationship('outlet', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('order_type')
                    ->label('Order Type')
                    ->options([
                        'dine_in' => 'Dine In',
                        'takeaway' => 'Takeaway',
                        'delivery' => 'Delivery',
                        'pickup' => 'Pickup',
                    ]),

                Tables\Filters\Filter::make('transaction_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('From Date'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Until Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('transaction_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('transaction_date', '<=', $date),
                            );
                    }),

                Tables\Filters\Filter::make('amount_range')
                    ->form([
                        Forms\Components\TextInput::make('min_amount')
                            ->label('Minimum Amount')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('max_amount')
                            ->label('Maximum Amount')
                            ->numeric()
                            ->prefix('Rp'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_amount'],
                                fn(Builder $query, $amount): Builder => $query->where('net_amount', '>=', $amount),
                            )
                            ->when(
                                $data['max_amount'],
                                fn(Builder $query, $amount): Builder => $query->where('net_amount', '<=', $amount),
                            );
                    }),

                Tables\Filters\TernaryFilter::make('is_offline_transaction')
                    ->label('Sync Status')
                    ->placeholder('All transactions')
                    ->trueLabel('Offline transactions')
                    ->falseLabel('Online transactions'),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('transaction_date', 'desc')
            ->defaultPaginationPageOption(25)
            ->paginationPageOptions([10, 25, 50, 100]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Transaction Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('transaction_number')
                            ->label('Transaction Number')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('transaction_date')
                            ->label('Date & Time')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('customer.nama')
                            ->label('Customer')
                            ->placeholder('Walk-in Customer'),
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Cashier'),
                        Infolists\Components\TextEntry::make('outlet.name')
                            ->label('Outlet')
                            ->badge()
                            ->color('info'),
                        Infolists\Components\TextEntry::make('order_type')
                            ->label('Order Type')
                            ->badge()
                            ->color(fn(?string $state): string => match ($state) {
                                'dine_in' => 'success',
                                'takeaway' => 'warning',
                                'delivery' => 'info',
                                'pickup' => 'primary',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn(?string $state): string => match ($state) {
                                'dine_in' => 'Dine In',
                                'takeaway' => 'Takeaway',
                                'delivery' => 'Delivery',
                                'pickup' => 'Pickup',
                                default => 'N/A',
                            }),
                        Infolists\Components\TextEntry::make('table_number')
                            ->label('Table Number')
                            ->placeholder('Takeaway'),
                        Infolists\Components\IconEntry::make('is_offline_transaction')
                            ->label('Sync Status')
                            ->boolean()
                            ->trueIcon('heroicon-o-wifi')
                            ->falseIcon('heroicon-o-cloud'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Payment Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('total_amount')
                            ->label('Subtotal')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('discount_amount')
                            ->label('Discount')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('tax_amount')
                            ->label('Tax')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('net_amount')
                            ->label('Total Amount')
                            ->money('IDR')
                            ->weight('bold'),
                        Infolists\Components\TextEntry::make('payment_method')
                            ->label('Payment Method')
                            ->badge(),
                        Infolists\Components\TextEntry::make('amount_paid')
                            ->label('Amount Paid')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('change_given')
                            ->label('Change Given')
                            ->money('IDR'),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Loyalty Points')
                    ->schema([
                        Infolists\Components\TextEntry::make('loyalty_points_used')
                            ->label('Points Used')
                            ->badge()
                            ->color('warning'),
                        Infolists\Components\TextEntry::make('loyalty_points_earned')
                            ->label('Points Earned')
                            ->badge()
                            ->color('success'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PosTransactionItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPosTransactions::route('/'),
            'create' => Pages\CreatePosTransaction::route('/create'),
            'view' => Pages\ViewPosTransaction::route('/{record}'),
            'edit' => Pages\EditPosTransaction::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
