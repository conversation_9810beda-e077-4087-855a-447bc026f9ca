<?php

namespace App\Filament\Pos\Resources;

use App\Filament\Pos\Resources\ProductResource\Pages;
use App\Filament\Pos\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use App\Models\Category;
use App\Services\PosCache;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Product Management';

    protected static ?string $navigationLabel = 'Products';

    protected static ?string $modelLabel = 'Product';

    protected static ?string $pluralModelLabel = 'Products';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Product Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Product Name')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('sku')
                            ->label('SKU')
                            ->unique(Product::class, 'sku', ignoreRecord: true)
                            ->maxLength(100),

                        Forms\Components\TextInput::make('barcode')
                            ->label('Barcode')
                            ->unique(Product::class, 'barcode', ignoreRecord: true)
                            ->maxLength(255),

                        Forms\Components\Select::make('category_id')
                            ->label('Category')
                            ->relationship('category', 'name')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('Category Name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\Textarea::make('description')
                                    ->label('Description')
                                    ->maxLength(500),
                            ])
                            ->required(),

                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\FileUpload::make('image')
                            ->label('Product Image')
                            ->image()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '1:1',
                                '4:3',
                                '16:9',
                            ])
                            ->directory('products')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'])
                            ->maxSize(2048) // 2MB
                            ->helperText('Upload a product image (max 2MB). Supported formats: JPEG, PNG, JPG, GIF, WebP')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Pricing & Inventory')
                    ->schema([
                        Forms\Components\TextInput::make('price')
                            ->label('Selling Price')
                            ->numeric()
                            ->prefix('Rp')
                            ->required()
                            ->minValue(0),

                        Forms\Components\TextInput::make('cost_price')
                            ->label('Cost Price')
                            ->numeric()
                            ->prefix('Rp')
                            ->minValue(0),

                        Forms\Components\TextInput::make('stock_quantity')
                            ->label('Stock Quantity')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->required(),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active Status')
                            ->default(true),

                        Forms\Components\Toggle::make('is_food_item')
                            ->label('Food Item')
                            ->helperText('Check if this is a food or beverage item')
                            ->default(false),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) =>
                $query->with(['category:id,name'])
                    ->select([
                        'id', 'name', 'sku', 'category_id', 'price', 'cost_price',
                        'stock_quantity', 'is_food_item', 'is_active', 'created_at'
                    ])
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\ImageColumn::make('image')
                    ->label('Image')
                    ->circular()
                    ->size(40)
                    ->defaultImageUrl(url('/images/no-image.png'))
                    ->toggleable(),

                Tables\Columns\TextColumn::make('sku')
                    ->label('SKU')
                    ->searchable()
                    ->copyable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('category.name')
                    ->label('Category')
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('price')
                    ->label('Price')
                    ->money('IDR')
                    ->sortable()
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('cost_price')
                    ->label('Cost Price')
                    ->money('IDR')
                    ->sortable()
                    ->alignEnd()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('stock_quantity')
                    ->label('Stock')
                    ->numeric()
                    ->sortable()
                    ->alignEnd()
                    ->badge()
                    ->color(fn ($state): string => match (true) {
                        $state > 50 => 'success',
                        $state > 10 => 'warning',
                        $state > 0 => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('profit_margin')
                    ->label('Profit Margin')
                    ->getStateUsing(function (Product $record): string {
                        if ($record->cost_price > 0) {
                            $margin = (($record->price - $record->cost_price) / $record->cost_price) * 100;
                            return number_format($margin, 1) . '%';
                        }
                        return 'N/A';
                    })
                    ->alignEnd()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('is_food_item')
                    ->label('Food Item')
                    ->boolean()
                    ->toggleable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('Category')
                    ->options(PosCache::getCategoryOptions())
                    ->searchable()
                    ->preload(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),

                Tables\Filters\TernaryFilter::make('is_food_item')
                    ->label('Food Item'),

                Tables\Filters\Filter::make('stock_level')
                    ->form([
                        Forms\Components\Select::make('stock_status')
                            ->label('Stock Status')
                            ->options([
                                'in_stock' => 'In Stock (> 0)',
                                'low_stock' => 'Low Stock (1-10)',
                                'out_of_stock' => 'Out of Stock (0)',
                                'high_stock' => 'High Stock (> 50)',
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['stock_status'],
                            function (Builder $query, $status) {
                                return match ($status) {
                                    'in_stock' => $query->where('stock_quantity', '>', 0),
                                    'low_stock' => $query->whereBetween('stock_quantity', [1, 10]),
                                    'out_of_stock' => $query->where('stock_quantity', 0),
                                    'high_stock' => $query->where('stock_quantity', '>', 50),
                                    default => $query,
                                };
                            }
                        );
                    }),

                Tables\Filters\Filter::make('price_range')
                    ->form([
                        Forms\Components\TextInput::make('min_price')
                            ->label('Minimum Price')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('max_price')
                            ->label('Maximum Price')
                            ->numeric()
                            ->prefix('Rp'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_price'],
                                fn (Builder $query, $price): Builder => $query->where('price', '>=', $price),
                            )
                            ->when(
                                $data['max_price'],
                                fn (Builder $query, $price): Builder => $query->where('price', '<=', $price),
                            );
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    
                    Tables\Actions\BulkAction::make('update_stock')
                        ->label('Update Stock')
                        ->icon('heroicon-o-cube')
                        ->form([
                            Forms\Components\Select::make('action')
                                ->label('Action')
                                ->options([
                                    'add' => 'Add Stock',
                                    'subtract' => 'Subtract Stock',
                                    'set' => 'Set Stock',
                                ])
                                ->required(),
                            Forms\Components\TextInput::make('quantity')
                                ->label('Quantity')
                                ->numeric()
                                ->required()
                                ->minValue(0),
                        ])
                        ->action(function (array $data, $records): void {
                            foreach ($records as $record) {
                                match ($data['action']) {
                                    'add' => $record->increment('stock_quantity', $data['quantity']),
                                    'subtract' => $record->decrement('stock_quantity', $data['quantity']),
                                    'set' => $record->update(['stock_quantity' => $data['quantity']]),
                                };
                            }
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->defaultPaginationPageOption(25)
            ->paginationPageOptions([10, 25, 50, 100]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Product Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->label('Product Name'),

                        Infolists\Components\ImageEntry::make('image')
                            ->label('Product Image')
                            ->height(200)
                            ->width(200)
                            ->defaultImageUrl(url('/images/no-image.png'))
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('sku')
                            ->label('SKU')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('barcode')
                            ->label('Barcode')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('category.name')
                            ->label('Category')
                            ->badge(),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Pricing & Inventory')
                    ->schema([
                        Infolists\Components\TextEntry::make('price')
                            ->label('Selling Price')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('cost_price')
                            ->label('Cost Price')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('profit_margin')
                            ->label('Profit Margin')
                            ->getStateUsing(function (Product $record): string {
                                if ($record->cost_price > 0) {
                                    $margin = (($record->price - $record->cost_price) / $record->cost_price) * 100;
                                    return number_format($margin, 1) . '%';
                                }
                                return 'N/A';
                            }),
                        Infolists\Components\TextEntry::make('stock_quantity')
                            ->label('Current Stock')
                            ->badge()
                            ->color(fn ($state): string => match (true) {
                                $state > 50 => 'success',
                                $state > 10 => 'warning',
                                $state > 0 => 'danger',
                                default => 'gray',
                            }),
                        Infolists\Components\IconEntry::make('is_food_item')
                            ->label('Food Item')
                            ->boolean(),
                        Infolists\Components\IconEntry::make('is_active')
                            ->label('Active Status')
                            ->boolean(),
                    ])
                    ->columns(3),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PosTransactionItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'view' => Pages\ViewProduct::route('/{record}'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
