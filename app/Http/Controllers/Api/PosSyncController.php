<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PosTransaction;
use App\Models\PosTransactionItem;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Category;
use App\Models\Entitas;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Carbon\Carbon;

class PosSyncController extends Controller
{
    /**
     * Sync transactions from POS to backoffice
     */
    public function syncTransactions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transactions' => 'required|array',
            'transactions.*.transaction_number' => 'required|string|unique:pos_transactions,transaction_number',
            'transactions.*.customer_id' => 'nullable|exists:customers,id',
            'transactions.*.outlet_id' => 'nullable|exists:outlets,id',
            'transactions.*.order_type' => 'nullable|string|in:dine_in,takeaway,delivery,pickup',
            'transactions.*.transaction_date' => 'required|date',
            'transactions.*.total_amount' => 'required|numeric|min:0',
            'transactions.*.discount_amount' => 'nullable|numeric|min:0',
            'transactions.*.tax_amount' => 'nullable|numeric|min:0',
            'transactions.*.net_amount' => 'required|numeric|min:0',
            'transactions.*.payment_method' => 'required|string',
            'transactions.*.amount_paid' => 'required|numeric|min:0',
            'transactions.*.change_given' => 'nullable|numeric|min:0',
            'transactions.*.table_number' => 'nullable|string',
            'transactions.*.items' => 'required|array|min:1',
            'transactions.*.items.*.product_id' => 'required|exists:products,id',
            'transactions.*.items.*.quantity' => 'required|integer|min:1',
            'transactions.*.items.*.unit_price' => 'required|numeric|min:0',
            'transactions.*.items.*.discount_per_item' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $syncedTransactions = [];
        $failedTransactions = [];

        DB::beginTransaction();
        try {
            foreach ($request->transactions as $transactionData) {
                try {
                    // Create transaction
                    $transaction = PosTransaction::create([
                        'transaction_number' => $transactionData['transaction_number'],
                        'customer_id' => $transactionData['customer_id'] ?? null,
                        'outlet_id' => $transactionData['outlet_id'] ?? null,
                        'order_type' => $transactionData['order_type'] ?? null,
                        'user_id' => $request->user()->id,
                        'transaction_date' => $transactionData['transaction_date'],
                        'total_amount' => $transactionData['total_amount'],
                        'discount_amount' => $transactionData['discount_amount'] ?? 0,
                        'tax_amount' => $transactionData['tax_amount'] ?? 0,
                        'net_amount' => $transactionData['net_amount'],
                        'payment_method' => $transactionData['payment_method'],
                        'amount_paid' => $transactionData['amount_paid'],
                        'change_given' => $transactionData['change_given'] ?? 0,
                        'table_number' => $transactionData['table_number'] ?? null,
                        'is_offline_transaction' => true,
                        'synced_at' => now(),
                    ]);

                    // Create transaction items
                    foreach ($transactionData['items'] as $itemData) {
                        PosTransactionItem::create([
                            'pos_transaction_id' => $transaction->id,
                            'product_id' => $itemData['product_id'],
                            'quantity' => $itemData['quantity'],
                            'unit_price' => $itemData['unit_price'],
                            'discount_per_item' => $itemData['discount_per_item'] ?? 0,
                        ]);

                        // Update product stock
                        $product = Product::find($itemData['product_id']);
                        if ($product) {
                            $product->decrement('stock_quantity', $itemData['quantity']);
                        }
                    }

                    $syncedTransactions[] = [
                        'transaction_number' => $transaction->transaction_number,
                        'id' => $transaction->id,
                        'status' => 'synced'
                    ];

                } catch (\Exception $e) {
                    $failedTransactions[] = [
                        'transaction_number' => $transactionData['transaction_number'],
                        'error' => $e->getMessage()
                    ];
                }
            }

            DB::commit();

            return response()->json([
                'message' => 'Sync completed',
                'synced_count' => count($syncedTransactions),
                'failed_count' => count($failedTransactions),
                'synced_transactions' => $syncedTransactions,
                'failed_transactions' => $failedTransactions,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Sync failed',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get products for POS system
     */
    public function getProducts(Request $request)
    {
        // Validate outlet_id parameter
        $request->validate([
            'outlet_id' => 'nullable|exists:outlets,id',
        ]);

        // Create cache key based on request parameters
        $cacheKey = 'pos_sync_products_' . md5(serialize($request->all()));

        // Cache for 5 minutes to reduce database load
        $result = \Cache::remember($cacheKey, 300, function () use ($request) {
            $outletId = $request->outlet_id;
            $outlet = $outletId ? \App\Models\Outlet::find($outletId) : null;

            $query = Product::with('category')
                ->where('is_active', true);

            // Filter by category if provided
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // Search by name or SKU
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('sku', 'like', "%{$search}%");
                });
            }

            // Filter by stock availability
            if ($request->has('in_stock') && $request->in_stock) {
                $query->where('stock_quantity', '>', 0);
            }

            // Add pagination support
            $perPage = $request->get('per_page', 100); // Default 100 items per page
            $page = $request->get('page', 1);

            if ($request->has('paginate') && $request->paginate) {
                $products = $query->paginate($perPage, ['id', 'name', 'sku', 'barcode', 'description', 'price', 'cost_price', 'category_id', 'stock_quantity', 'is_food_item', 'image']);

                // Apply outlet-specific pricing if outlet is provided
                $productsWithPricing = $outlet ?
                    collect($products->items())->map(function ($product) use ($outlet) {
                        $product->outlet_price = $outlet->getProductPrice($product->id);
                        $product->outlet_cost_price = $outlet->getProductCostPrice($product->id);
                        $product->default_price = $product->price;
                        $product->price = $product->outlet_price; // Override with outlet price
                        $product->category_name = $product->category?->name;
                        $product->image_url = $product->image ? asset('storage/' . $product->image) : null;
                        return $product;
                    })->toArray() : collect($products->items())->map(function ($product) {
                        $product->category_name = $product->category?->name;
                        $product->image_url = $product->image ? asset('storage/' . $product->image) : null;
                        return $product;
                    })->toArray();

                return [
                    'products' => $productsWithPricing,
                    'pagination' => [
                        'current_page' => $products->currentPage(),
                        'last_page' => $products->lastPage(),
                        'per_page' => $products->perPage(),
                        'total' => $products->total(),
                        'has_more_pages' => $products->hasMorePages(),
                    ],
                    'outlet_id' => $outletId,
                    'outlet_name' => $outlet ? $outlet->name : null,
                    'last_updated' => now()->toISOString(),
                ];
            } else {
                // Return all products (existing behavior)
                $products = $query->get(['id', 'name', 'sku', 'barcode', 'description', 'price', 'cost_price', 'category_id', 'stock_quantity', 'is_food_item', 'image']);

                // Apply outlet-specific pricing if outlet is provided
                $productsWithPricing = $outlet ?
                    $products->map(function ($product) use ($outlet) {
                        $product->outlet_price = $outlet->getProductPrice($product->id);
                        $product->outlet_cost_price = $outlet->getProductCostPrice($product->id);
                        $product->default_price = $product->price;
                        $product->price = $product->outlet_price; // Override with outlet price
                        $product->category_name = $product->category?->name;
                        $product->image_url = $product->image ? asset('storage/' . $product->image) : null;
                        return $product;
                    }) : $products->map(function ($product) {
                        $product->category_name = $product->category?->name;
                        $product->image_url = $product->image ? asset('storage/' . $product->image) : null;
                        return $product;
                    });

                return [
                    'products' => $productsWithPricing,
                    'total_count' => $productsWithPricing->count(),
                    'outlet_id' => $outletId,
                    'outlet_name' => $outlet ? $outlet->name : null,
                    'last_updated' => now()->toISOString(),
                ];
            }
        });

        return response()->json($result);
    }

    /**
     * Get customers for POS system
     */
    public function getCustomers(Request $request)
    {
        $query = Customer::where('is_active', true);

        // Search by name, email, or phone
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nama', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('telepon', 'like', "%{$search}%");
            });
        }

        $customers = $query->get(['id', 'nama', 'email', 'telepon', 'loyalty_points']);

        return response()->json([
            'customers' => $customers,
            'last_updated' => now()->toISOString(),
        ]);
    }

    /**
     * Update customer loyalty points
     */
    public function updateLoyaltyPoints(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'points' => 'required|integer',
            'transaction_number' => 'required|string',
            'type' => 'required|in:earned,redeemed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        try {
            $customer = Customer::find($request->customer_id);
            
            if ($request->type === 'earned') {
                $customer->increment('loyalty_points', $request->points);
            } else {
                $customer->decrement('loyalty_points', $request->points);
            }

            return response()->json([
                'message' => 'Loyalty points updated successfully',
                'customer' => [
                    'id' => $customer->id,
                    'nama' => $customer->nama,
                    'loyalty_points' => $customer->loyalty_points,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update loyalty points',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get sync status and statistics
     */
    public function getSyncStatus(Request $request)
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();

        $stats = [
            'total_transactions' => PosTransaction::count(),
            'offline_transactions' => PosTransaction::where('is_offline_transaction', true)->count(),
            'today_transactions' => PosTransaction::whereDate('transaction_date', $today)->count(),
            'month_transactions' => PosTransaction::where('transaction_date', '>=', $thisMonth)->count(),
            'last_sync' => PosTransaction::where('is_offline_transaction', true)
                ->latest('synced_at')
                ->value('synced_at'),
            'pending_sync_count' => 0, // This would come from POS system
        ];

        // Add rate limit debugging info
        $user = $request->user();
        $ip = $request->ip();

        $rateLimitInfo = [
            'user_id' => $user?->id,
            'ip_address' => $ip,
            'api_rate_limit_remaining' => \RateLimiter::remaining('api:' . ($user?->id ?: $ip), 60),
            'pos_sync_rate_limit_remaining' => \RateLimiter::remaining('pos_sync:' . ($user?->id ?: $ip), 300),
        ];

        return response()->json([
            'sync_status' => 'online',
            'statistics' => $stats,
            'rate_limit_debug' => $rateLimitInfo,
            'server_time' => now()->toISOString(),
        ]);
    }

    /**
     * Create a new product from POS
     */
    public function createProduct(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'sku' => 'nullable|string|max:100|unique:products,sku',
            'barcode' => 'nullable|string|max:100|unique:products,barcode',
            'description' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'category_id' => 'nullable|exists:categories,id',
            'stock_quantity' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean',
            'is_food_item' => 'nullable|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048', // Max 2MB
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        try {
            DB::beginTransaction();

            // Create a default category if none exists and category_id is null
            $categoryId = $request->category_id;
            if (!$categoryId) {
                // Try to find or create a default category
                $defaultCategory = Category::firstOrCreate(
                    ['name' => 'Default'],
                    ['description' => 'Default category for POS products', 'is_active' => true]
                );
                $categoryId = $defaultCategory->id;
            }

            // Handle image upload
            $imagePath = null;
            if ($request->hasFile('image') && $request->file('image')->isValid()) {
                $image = $request->file('image');
                $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();

                // Ensure the products directory exists
                $productsPath = storage_path('app/public/products');
                if (!file_exists($productsPath)) {
                    mkdir($productsPath, 0755, true);
                }

                // Move the file manually
                $fullPath = $productsPath . DIRECTORY_SEPARATOR . $imageName;
                if ($image->move($productsPath, $imageName)) {
                    $imagePath = 'products/' . $imageName;
                }
            }

            $product = Product::create([
                'name' => $request->name,
                'sku' => $request->sku,
                'barcode' => $request->barcode,
                'description' => $request->description,
                'price' => $request->price,
                'cost_price' => $request->cost_price ?? 0,
                'category_id' => $categoryId,
                'stock_quantity' => $request->stock_quantity ?? 0,
                'is_active' => $request->is_active ?? true,
                'is_food_item' => $request->is_food_item ?? false,
                'image' => $imagePath,
            ]);

            DB::commit();

            // Clear cache
            Cache::forget('pos_product_options');

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'data' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'barcode' => $product->barcode,
                    'price' => $product->price,
                    'cost_price' => $product->cost_price,
                    'category_id' => $product->category_id,
                    'category_name' => $product->category?->name,
                    'stock_quantity' => $product->stock_quantity,
                    'is_active' => $product->is_active,
                    'is_food_item' => $product->is_food_item,
                    'image' => $product->image,
                    'image_url' => $product->image ? asset('storage/' . $product->image) : null,
                    'created_at' => $product->created_at->toISOString(),
                ]
            ], Response::HTTP_CREATED);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to create product',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Create a new customer from POS
     */
    public function createCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nama' => 'required|string|max:255',
            'email' => 'nullable|email|max:255|unique:customers,email',
            'telepon' => 'nullable|string|max:20',
            'alamat' => 'nullable|string|max:500',
            'tanggal_lahir' => 'nullable|date',
            'jenis_kelamin' => 'nullable|in:L,P',
            'loyalty_points' => 'nullable|integer|min:0',
            'segment' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'nullable|boolean',
            'province_id' => 'nullable|integer',
            'city_id' => 'nullable|integer',
            'district_id' => 'nullable|integer',
            'village_id' => 'nullable|integer',
            'postal_code' => 'nullable|string|max:10',
            'detail_address' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        try {
            DB::beginTransaction();

            $customer = Customer::create([
                'nama' => $request->nama,
                'email' => $request->email,
                'telepon' => $request->telepon,
                'alamat' => $request->alamat,
                'tanggal_lahir' => $request->tanggal_lahir,
                'jenis_kelamin' => $request->jenis_kelamin,
                'loyalty_points' => $request->loyalty_points ?? 0,
                'segment' => $request->segment,
                'notes' => $request->notes,
                'is_active' => $request->is_active ?? true,
                'province_id' => $request->province_id,
                'city_id' => $request->city_id,
                'district_id' => $request->district_id,
                'village_id' => $request->village_id,
                'postal_code' => $request->postal_code,
                'detail_address' => $request->detail_address,
            ]);

            DB::commit();

            // Clear cache
            Cache::forget('pos_customer_options');

            return response()->json([
                'success' => true,
                'message' => 'Customer created successfully',
                'data' => [
                    'id' => $customer->id,
                    'nama' => $customer->nama,
                    'email' => $customer->email,
                    'telepon' => $customer->telepon,
                    'alamat' => $customer->alamat,
                    'tanggal_lahir' => $customer->tanggal_lahir,
                    'jenis_kelamin' => $customer->jenis_kelamin,
                    'loyalty_points' => $customer->loyalty_points,
                    'segment' => $customer->segment,
                    'is_active' => $customer->is_active,
                    'province_id' => $customer->province_id,
                    'city_id' => $customer->city_id,
                    'district_id' => $customer->district_id,
                    'village_id' => $customer->village_id,
                    'postal_code' => $customer->postal_code,
                    'detail_address' => $customer->detail_address,
                    'created_at' => $customer->created_at->toISOString(),
                ]
            ], Response::HTTP_CREATED);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to create customer',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
