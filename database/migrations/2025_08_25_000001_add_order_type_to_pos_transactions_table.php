<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pos_transactions', function (Blueprint $table) {
            if (!Schema::hasColumn('pos_transactions', 'order_type')) {
                $table->enum('order_type', ['dine_in', 'takeaway', 'delivery', 'pickup'])
                    ->nullable()
                    ->after('outlet_id')
                    ->comment('Type of order: dine_in, takeaway, delivery, pickup');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pos_transactions', function (Blueprint $table) {
            if (Schema::hasColumn('pos_transactions', 'order_type')) {
                $table->dropColumn('order_type');
            }
        });
    }
};
