<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Test route to check if middleware is working
Route::get('/test-middleware', function () {
    return response()->json(['message' => 'Middleware test successful', 'timestamp' => now()]);
});

// POS Authentication Routes
Route::prefix('pos')->middleware(['pos_api', 'throttle:pos_auth'])->group(function () {
    Route::post('/login', [App\Http\Controllers\Api\PosAuthController::class, 'login']);

    Route::middleware(['auth:sanctum', 'throttle:pos_general'])->group(function () {
        Route::post('/logout', [App\Http\Controllers\Api\PosAuthController::class, 'logout']);
        Route::post('/logout-all', [App\Http\Controllers\Api\PosAuthController::class, 'logoutAll']);
        Route::get('/user', [App\Http\Controllers\Api\PosAuthController::class, 'user']);
        Route::post('/refresh', [App\Http\Controllers\Api\PosAuthController::class, 'refresh']);
        Route::get('/tokens', [App\Http\Controllers\Api\PosAuthController::class, 'tokens']);
        Route::delete('/tokens/{tokenId}', [App\Http\Controllers\Api\PosAuthController::class, 'revokeToken']);
    });
});

// POS Sync Routes (require authentication and specific abilities)
Route::middleware(['pos_api', 'auth:sanctum', \Laravel\Sanctum\Http\Middleware\CheckAbilities::class . ':pos:sync', 'throttle:pos_sync'])->prefix('pos/sync')->group(function () {
    Route::post('/transactions', [App\Http\Controllers\Api\PosSyncController::class, 'syncTransactions']);
    Route::post('/loyalty-points', [App\Http\Controllers\Api\PosSyncController::class, 'updateLoyaltyPoints']);
    Route::get('/products', [App\Http\Controllers\Api\PosSyncController::class, 'getProducts']);
    Route::get('/customers', [App\Http\Controllers\Api\PosSyncController::class, 'getCustomers']);
    Route::get('/status', [App\Http\Controllers\Api\PosSyncController::class, 'getSyncStatus']);

    // New endpoints for creating products and customers from POS
    Route::post('/products', [App\Http\Controllers\Api\PosSyncController::class, 'createProduct']);
    Route::post('/customers', [App\Http\Controllers\Api\PosSyncController::class, 'createCustomer']);
});

// Test route for POS products with outlet-specific pricing (for debugging)
Route::get('/pos/test/products', function (Request $request) {
    try {
        $outletId = $request->get('outlet_id', 1); // Default to outlet 1
        $outlet = \App\Models\Outlet::find($outletId);

        if (!$outlet) {
            return response()->json([
                'success' => false,
                'message' => 'Outlet not found',
                'outlet_id' => $outletId,
            ], 404);
        }

        $products = \App\Models\Product::where('is_active', true)->limit(5)->get(['id', 'name', 'sku', 'price']);

        $productsWithOutletPricing = $products->map(function ($product) use ($outlet) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'default_price' => $product->price,
                'outlet_price' => $outlet->getProductPrice($product->id),
                'outlet_cost_price' => $outlet->getProductCostPrice($product->id),
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'Test successful with outlet-specific pricing',
            'outlet' => [
                'id' => $outlet->id,
                'name' => $outlet->name,
            ],
            'products_count' => $productsWithOutletPricing->count(),
            'sample_products' => $productsWithOutletPricing,
            'timestamp' => now()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'timestamp' => now()
        ], 500);
    }
});

// POS Product Management Routes
Route::middleware(['pos_api', 'auth:sanctum', \Laravel\Sanctum\Http\Middleware\CheckAbilities::class . ':pos:products', 'throttle:pos_general'])->prefix('pos/products')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\PosProductController::class, 'index']);
    Route::get('/{id}', [App\Http\Controllers\Api\PosProductController::class, 'show']);
    Route::post('/{id}/stock', [App\Http\Controllers\Api\PosProductController::class, 'updateStock']);
    Route::post('/check-availability', [App\Http\Controllers\Api\PosProductController::class, 'checkAvailability']);
    Route::get('/inventory/low-stock', [App\Http\Controllers\Api\PosProductController::class, 'lowStock']);
    Route::get('/inventory/out-of-stock', [App\Http\Controllers\Api\PosProductController::class, 'outOfStock']);
    Route::post('/bulk/update-prices', [App\Http\Controllers\Api\PosProductController::class, 'bulkUpdatePrices']);
});

// POS Product Categories Routes
Route::middleware(['pos_api', 'auth:sanctum', \Laravel\Sanctum\Http\Middleware\CheckAbilities::class . ':pos:products', 'throttle:pos_general'])->prefix('pos/categories')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\PosProductController::class, 'categories']);
});

// POS Customer Management Routes
Route::middleware(['pos_api', 'auth:sanctum', \Laravel\Sanctum\Http\Middleware\CheckAbilities::class . ':pos:customers', 'throttle:pos_general'])->prefix('pos/customers')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\PosCustomerController::class, 'index']);
    Route::post('/', [App\Http\Controllers\Api\PosCustomerController::class, 'store']);
    Route::get('/search', [App\Http\Controllers\Api\PosCustomerController::class, 'search']);
    Route::get('/segments', [App\Http\Controllers\Api\PosCustomerController::class, 'segments']);
    Route::get('/{id}', [App\Http\Controllers\Api\PosCustomerController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\PosCustomerController::class, 'update']);
    Route::post('/{id}/loyalty-points', [App\Http\Controllers\Api\PosCustomerController::class, 'updateLoyaltyPoints']);
    Route::get('/{id}/transactions', [App\Http\Controllers\Api\PosCustomerController::class, 'transactions']);
});

// POS Reports Routes
Route::middleware(['pos_api', 'auth:sanctum', \Laravel\Sanctum\Http\Middleware\CheckAbilities::class . ':pos:read', 'throttle:pos_general'])->prefix('pos/reports')->group(function () {
    Route::get('/sales-summary', [App\Http\Controllers\Api\PosReportController::class, 'salesSummary']);
    Route::get('/top-products', [App\Http\Controllers\Api\PosReportController::class, 'topProducts']);
    Route::get('/customer-analytics', [App\Http\Controllers\Api\PosReportController::class, 'customerAnalytics']);
    Route::get('/location-performance', [App\Http\Controllers\Api\PosReportController::class, 'locationPerformance']);
    Route::get('/inventory', [App\Http\Controllers\Api\PosReportController::class, 'inventoryReport']);
    Route::get('/financial-summary', [App\Http\Controllers\Api\PosReportController::class, 'financialSummary']);
});

// POS Price Management Routes
Route::middleware(['pos_api', 'auth:sanctum', \Laravel\Sanctum\Http\Middleware\CheckAbilities::class . ':pos:read', 'throttle:pos_general'])->prefix('pos/prices')->group(function () {
    Route::get('/products', [App\Http\Controllers\Api\PosPriceController::class, 'getProductPrices']);
    Route::get('/products/{productId}', [App\Http\Controllers\Api\PosPriceController::class, 'getProductPrice']);
    Route::get('/outlets/{outletId}/price-lists', [App\Http\Controllers\Api\PosPriceController::class, 'getOutletPriceLists']);
    Route::get('/products/{productId}/comparison', [App\Http\Controllers\Api\PosPriceController::class, 'getProductPriceComparison']);
    Route::post('/bulk', [App\Http\Controllers\Api\PosPriceController::class, 'bulkGetProductPrices']);
    Route::get('/debug/{productId}/{outletId}', [App\Http\Controllers\Api\PosPriceController::class, 'debugPriceResolution']);
    Route::get('/validate-outlet/{outletId}', [App\Http\Controllers\Api\PosPriceController::class, 'validateOutletConfiguration']);
});
